#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

async function verifyWordPackages(): Promise<void> {
	console.log('🔍 Verifying Word Packages Structure...\n');

	try {
		// Connect to database
		await prisma.$connect();

		// Get all word packages with their words
		const packages = await prisma.wordPackage.findMany({
			include: {
				words: true,
			},
			orderBy: [
				{ source_language: 'asc' },
				{ target_language: 'asc' },
				{ difficulty: 'asc' },
				{ name: 'asc' },
			],
		});

		// Also get some sample words with full definitions
		const sampleWords = await prisma.word.findMany({
			take: 5,
			include: {
				definitions: {
					include: {
						explains: true,
						examples: true,
					},
				},
			},
		});

		console.log(`📦 Total Word Packages: ${packages.length}\n`);

		// Group by language direction
		const englishPackages = packages.filter(
			(p) => p.source_language === 'EN' && p.target_language === 'VI'
		);
		const vietnamesePackages = packages.filter(
			(p) => p.source_language === 'VI' && p.target_language === 'EN'
		);

		console.log(
			'🇺🇸 ➡️ 🇻🇳 ENGLISH PACKAGES (Learning English words with Vietnamese explanations):'
		);
		console.log('='.repeat(80));
		englishPackages.forEach((pkg, index) => {
			console.log(`${index + 1}. ${pkg.name}`);
			console.log(`   📝 Description: ${pkg.description}`);
			console.log(`   🎯 Difficulty: ${pkg.difficulty}`);
			console.log(`   📂 Category: ${pkg.category}`);
			console.log(`   🏷️  Tags: ${pkg.tags.join(', ')}`);
			console.log(`   📊 Word Count: ${pkg.word_count}`);
			console.log(
				`   🔤 Sample Words: ${pkg.words
					.slice(0, 5)
					.map((w) => w.term)
					.join(', ')}...`
			);
			console.log(
				`   ✅ Language Consistency: ${
					pkg.words.every((w) => w.language === pkg.source_language) ? 'CORRECT' : 'ERROR'
				}`
			);
			console.log('');
		});

		console.log(
			'\n🇻🇳 ➡️ 🇺🇸 VIETNAMESE PACKAGES (Learning Vietnamese words with English explanations):'
		);
		console.log('='.repeat(80));
		vietnamesePackages.forEach((pkg, index) => {
			console.log(`${index + 1}. ${pkg.name}`);
			console.log(`   📝 Description: ${pkg.description}`);
			console.log(`   🎯 Difficulty: ${pkg.difficulty}`);
			console.log(`   📂 Category: ${pkg.category}`);
			console.log(`   🏷️  Tags: ${pkg.tags.join(', ')}`);
			console.log(`   📊 Word Count: ${pkg.word_count}`);
			console.log(
				`   🔤 Sample Words: ${pkg.words
					.slice(0, 5)
					.map((w) => w.term)
					.join(', ')}...`
			);
			console.log(
				`   ✅ Language Consistency: ${
					pkg.words.every((w) => w.language === pkg.source_language) ? 'CORRECT' : 'ERROR'
				}`
			);
			console.log('');
		});

		// Summary statistics
		console.log('\n📊 SUMMARY STATISTICS:');
		console.log('='.repeat(50));
		console.log(`Total Packages: ${packages.length}`);
		console.log(`English Packages (EN → VI): ${englishPackages.length}`);
		console.log(`Vietnamese Packages (VI → EN): ${vietnamesePackages.length}`);

		const difficultyStats = packages.reduce((acc, pkg) => {
			acc[pkg.difficulty] = (acc[pkg.difficulty] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		console.log('\nBy Difficulty:');
		Object.entries(difficultyStats).forEach(([difficulty, count]) => {
			console.log(`  ${difficulty}: ${count} packages`);
		});

		const categoryStats = packages.reduce((acc, pkg) => {
			acc[pkg.category] = (acc[pkg.category] || 0) + 1;
			return acc;
		}, {} as Record<string, number>);

		console.log('\nBy Category:');
		Object.entries(categoryStats).forEach(([category, count]) => {
			console.log(`  ${category}: ${count} packages`);
		});

		// Verify language consistency (now checking target_language)
		const inconsistentPackages = packages.filter(
			(pkg) => !pkg.words.every((w) => w.language === pkg.target_language)
		);

		if (inconsistentPackages.length > 0) {
			console.log('\n❌ LANGUAGE CONSISTENCY ERRORS:');
			inconsistentPackages.forEach((pkg) => {
				console.log(
					`  - ${pkg.name}: Expected ${pkg.target_language}, found mixed languages`
				);
			});
		} else {
			console.log('\n✅ All packages have consistent language mapping (target_language)!');
		}

		// Display sample words with full structure
		console.log('\n📖 SAMPLE WORDS WITH FULL DEFINITIONS:');
		console.log('='.repeat(50));
		sampleWords.forEach((word, index) => {
			console.log(`${index + 1}. ${word.term} (${word.language})`);
			word.definitions.forEach((def, defIndex) => {
				console.log(`   Definition ${defIndex + 1}:`);
				console.log(`   📝 POS: ${def.pos.join(', ')}`);
				console.log(`   🔊 IPA: ${def.ipa}`);

				if (def.explains.length > 0) {
					console.log(`   💡 Explanations:`);
					def.explains.forEach((explain, expIndex) => {
						console.log(`      ${expIndex + 1}. EN: ${explain.EN}`);
						console.log(`         VI: ${explain.VI}`);
					});
				}

				if (def.examples.length > 0) {
					console.log(`   📚 Examples:`);
					def.examples.forEach((example, exIndex) => {
						console.log(`      ${exIndex + 1}. EN: ${example.EN}`);
						console.log(`         VI: ${example.VI}`);
					});
				}
				console.log('');
			});
		});

		console.log('\n🎉 Verification completed successfully!');
	} catch (error) {
		console.error('❌ Verification failed:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Main execution
async function main(): Promise<void> {
	await verifyWordPackages();
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { verifyWordPackages };
