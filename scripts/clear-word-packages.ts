#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

async function clearWordPackages(): Promise<void> {
	console.log('🗑️  Clearing Word Packages...\n');

	try {
		// Connect to database
		await prisma.$connect();

		// Delete in correct order due to foreign key constraints
		console.log('Deleting UserWordPackage entries...');
		await prisma.userWordPackage.deleteMany({});

		console.log('Deleting WordPackageWord entries...');
		await prisma.wordPackageWord.deleteMany({});

		console.log('Deleting WordPackage entries...');
		await prisma.wordPackage.deleteMany({});

		console.log('Deleting Example entries...');
		await prisma.example.deleteMany({});

		console.log('Deleting Explain entries...');
		await prisma.explain.deleteMany({});

		console.log('Deleting Definition entries...');
		await prisma.definition.deleteMany({});

		console.log('Deleting Word entries...');
		await prisma.word.deleteMany({});

		console.log('\n✅ All word packages and related data cleared successfully!');

	} catch (error) {
		console.error('❌ Failed to clear word packages:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Main execution
async function main(): Promise<void> {
	await clearWordPackages();
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { clearWordPackages };
