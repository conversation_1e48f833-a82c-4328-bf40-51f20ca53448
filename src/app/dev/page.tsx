'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/components/ui';
import { useTranslation } from '@/contexts/translation-context';
import { 
	Bug, 
	Code, 
	Database, 
	Settings, 
	TestTube,
	Wrench,
	AlertTriangle,
	Info
} from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Only allow access in development
if (process.env.NODE_ENV !== 'development') {
	notFound();
}

export default function DevToolsPage() {
	const { t } = useTranslation();

	const devTools = [
		{
			name: 'Error Testing',
			description: 'Test different error scenarios and error boundaries',
			icon: <Bug className="h-6 w-6" />,
			href: '/dev/error-test',
			color: 'bg-red-500',
			available: true
		},
		{
			name: 'Component Testing',
			description: 'Test UI components in isolation',
			icon: <Code className="h-6 w-6" />,
			href: '/dev/components',
			color: 'bg-blue-500',
			available: false
		},
		{
			name: 'Database Testing',
			description: 'Test database operations and migrations',
			icon: <Database className="h-6 w-6" />,
			href: '/dev/database',
			color: 'bg-green-500',
			available: false
		},
		{
			name: 'API Testing',
			description: 'Test API endpoints and responses',
			icon: <TestTube className="h-6 w-6" />,
			href: '/dev/api',
			color: 'bg-purple-500',
			available: false
		},
		{
			name: 'Performance Testing',
			description: 'Test performance and optimization',
			icon: <Settings className="h-6 w-6" />,
			href: '/dev/performance',
			color: 'bg-orange-500',
			available: false
		},
		{
			name: 'Development Tools',
			description: 'Various development utilities',
			icon: <Wrench className="h-6 w-6" />,
			href: '/dev/tools',
			color: 'bg-gray-500',
			available: false
		}
	];

	return (
		<div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
			<div className="max-w-6xl mx-auto space-y-6">
				{/* Header */}
				<Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
					<CardHeader>
						<CardTitle className="flex items-center gap-2 text-blue-800 dark:text-blue-200">
							<Code className="h-6 w-6" />
							Development Tools
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<p className="text-blue-700 dark:text-blue-300">
								Welcome to the development tools dashboard. These tools are only available in development mode 
								and help with testing, debugging, and development workflows.
							</p>
							
							<div className="flex items-start gap-3 p-3 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
								<AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
								<div>
									<p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
										Development Only
									</p>
									<p className="text-sm text-yellow-700 dark:text-yellow-300">
										These pages are automatically blocked in production environments for security.
									</p>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Tools Grid */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{devTools.map((tool, index) => (
						<Card 
							key={index} 
							className={`hover:shadow-lg transition-all duration-200 ${
								!tool.available ? 'opacity-60' : 'hover:scale-105'
							}`}
						>
							<CardContent className="p-6">
								<div className="space-y-4">
									{/* Icon and Title */}
									<div className="flex items-center gap-3">
										<div className={`p-3 rounded-lg ${tool.color} text-white`}>
											{tool.icon}
										</div>
										<div className="flex-1">
											<h3 className="font-semibold text-gray-900 dark:text-white">
												{tool.name}
											</h3>
											{!tool.available && (
												<span className="text-xs text-gray-500 dark:text-gray-400">
													Coming Soon
												</span>
											)}
										</div>
									</div>

									{/* Description */}
									<p className="text-sm text-gray-600 dark:text-gray-400">
										{tool.description}
									</p>

									{/* Action Button */}
									{tool.available ? (
										<Link href={tool.href}>
											<Button className="w-full">
												Open Tool
											</Button>
										</Link>
									) : (
										<Button disabled className="w-full">
											Coming Soon
										</Button>
									)}
								</div>
							</CardContent>
						</Card>
					))}
				</div>

				{/* Environment Info */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Info className="h-5 w-5" />
							Environment Information
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							<div className="space-y-1">
								<p className="text-sm font-medium text-gray-700 dark:text-gray-300">
									Node Environment
								</p>
								<p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
									{process.env.NODE_ENV}
								</p>
							</div>
							
							<div className="space-y-1">
								<p className="text-sm font-medium text-gray-700 dark:text-gray-300">
									Next.js Version
								</p>
								<p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
									{process.env.NEXT_PUBLIC_VERSION || 'Unknown'}
								</p>
							</div>
							
							<div className="space-y-1">
								<p className="text-sm font-medium text-gray-700 dark:text-gray-300">
									Build Time
								</p>
								<p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
									{new Date().toLocaleString()}
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Quick Actions */}
				<Card>
					<CardHeader>
						<CardTitle>Quick Actions</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex flex-wrap gap-3">
							<Link href="/">
								<Button variant="outline">
									← Back to Home
								</Button>
							</Link>
							
							<Link href="/admin">
								<Button variant="outline">
									Admin Dashboard
								</Button>
							</Link>
							
							<Button 
								variant="outline"
								onClick={() => window.location.reload()}
							>
								Reload Page
							</Button>
							
							<Button 
								variant="outline"
								onClick={() => {
									if (typeof window !== 'undefined') {
										localStorage.clear();
										sessionStorage.clear();
										window.location.reload();
									}
								}}
							>
								Clear Storage
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
