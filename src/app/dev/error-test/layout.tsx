'use client';

import { ReactNode } from 'react';
import { ErrorBoundary } from '@/components/error/error-boundary';
import { notFound } from 'next/navigation';

// Only allow access in development
if (process.env.NODE_ENV !== 'development') {
	notFound();
}

interface DevErrorTestLayoutProps {
	children: ReactNode;
}

export default function DevErrorTestLayout({ children }: DevErrorTestLayoutProps) {
	return (
		<ErrorBoundary>
			{children}
		</ErrorBoundary>
	);
}
